from fastapi import FastAP<PERSON>, Request
from sse_starlette.sse import EventSourceResponse
import asyncio
# server.py
from mcp.server.fastmcp import FastMCP

# Create an MCP server
mcp = FastMCP("Demo")
app = FastAPI()

# 控制指令队列
messages = [
    {"command": "setPlaybackRate", "value": 1.5},
    {"command": "skipIntro"},
    # 可以继续添加更多指令
]

async def event_generator():
    for msg in messages:
        await asyncio.sleep(2)  # 每2秒推送一条指令
        yield {
            "event": "control",
            "data": str(msg)
        }
    while True:
        await asyncio.sleep(10)
        # 保持连接心跳
        yield {
            "event": "ping",
            "data": "keep-alive"
        }

@app.get("/sse")
async def sse(request: Request):
    return EventSourceResponse(event_generator())

@app.get("/")
async def root():
    return {"message": "Hello World"}
def main():
    print("Hello from mcp-server!")


if __name__ == "__main__":
    mcp.run(transport='sse')
